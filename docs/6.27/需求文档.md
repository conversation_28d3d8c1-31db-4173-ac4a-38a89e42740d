# 指令
帮我完成以下需求，每完成一小步，就同步更新在 docs/6.27/开发记录.md 中。

# 需求

## 1. 思考内容实时识别和渲染
对于思考模型，目前在输出思考内容的过程中，思考内容未被正确识别，因为此时可能标签还未闭合。比如`<think>xxx`但没有`</think>`。这导致了思考输出过程中，思考内容没有按思考内容的样式渲染。

**具体要求：**
- 在标签未闭合的情况下，也能正确提取思考内容
- 实现实时渲染思考内容的样式，即使`<think>`标签尚未闭合
- 确保思考内容在输出过程中就能正确应用样式

## 2. 思考内容动效优化
思考内容的动效需要优化。当模型正在输出思考内容时，前端展示类似广告牌的文字滚动效果。

**具体要求：**
- 文字从左到右出现
- 超过一行内容时，文字滚动到下一行
- 行间应有过渡效果
- 实现类似广告牌的滚动显示效果

## 3. 会话标题生成优化
目前首条消息回复后会赋予会话标题，可能会使用回复内容作为标题，如果是思考模型，这可能导致标题含有`<think>`标签字样。

**具体要求：**
- 确保被赋予标题的是回答的内容，而不是思考过程
- 过滤掉`<think>`标签及其内容，只使用实际回答内容作为标题
- 保证标题的清洁性和可读性

## 4. 思考内容样式优化
思考内容的样式需要优化。

**具体要求：**
- 思考区域应该是浅色背景和深色文字
- 左侧无border样式
- 展示一个4个角星星图标
- 在思考过程中播放旋转动画
- 确保视觉效果与其他消息内容有明显区分

## 开发要求
- 每完成一个需求点，立即更新 docs/6.27/开发记录.md
- 在开发记录中详细记录实现方案、修改的文件和关键代码变更
- 确保所有修改都经过测试验证

## 5. “重命名会话”弹窗样式优化。

- chatlist重命名会话时的弹窗，是绿色主题和直角样式，没有使用该app navie ui主题，这不符合预期，请变更实现方式，使用该app的自定义样式。