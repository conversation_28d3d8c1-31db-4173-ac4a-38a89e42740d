import { useStorage } from "@vueuse/core";
import { computed, ComputedRef, nextTick, ref } from "vue";
import { defineStore } from "pinia";
import type { Msg, Chat, MessageContent } from "@/core/types/chat.ts";
import { MessageStatus } from "@/core/types/chat.ts";
import { useModelStore } from "@/store/useModelStore.ts";
import { summaryService } from "@/services/summaryService.ts";
import { message } from "@/core/utils/message.ts";
import { processContent, hasThinkingContent, isThinkingCompleted } from "@/core/utils/messageUtils.ts";
import { chatStoreAdapter } from "@/adapters/ChatStoreAdapter";

// 回复风格类型定义
export type ReplyStyle = "default" | "concise" | "detailed";

// 回复风格配置
export const REPLY_STYLE_PROMPTS: Record<ReplyStyle, string> = {
    default: "",
    concise: "请用简洁明了的方式回答，避免冗长的解释。",
    detailed: "请提供详细、全面的回答，包含相关的背景信息和具体示例。"
};

export const REPLY_STYLE_LABELS: Record<ReplyStyle, string> = {
    default: "默认",
    concise: "简洁",
    detailed: "详细"
};

export const useChatStore = defineStore("chat", () => {
    const modelStore = useModelStore();
    const maxMsgCount = useStorage<number>("chat/maxMsgCount", 10);
    const enableAiSummary = useStorage<boolean>("chat/enableAiSummary", false);
    const activeChatIdStorage = ref<string>("0");
    const chatList = ref<Chat[]>([]);
    const isSidebarCollapsed = useStorage<boolean>(
        "config/isSidebarCollapsed",
        false
    );
    const isLoadingMessages = ref(false);
    const isLoadingChats = ref(false);
    const isLoading = ref(false); // Global loading state for sending messages

    // 回复风格状态
    const replyStyle = useStorage<ReplyStyle>("chat/replyStyle", "default");

    // --- Chat Management ---

    async function loadAllChats() {
        isLoadingChats.value = true;
        try {
            // 使用新的适配器加载所有聊天
            chatList.value = await chatStoreAdapter.loadAllChats();
            if (
                chatList.value.length === 0 &&
                activeChatIdStorage.value !== "0"
            ) {
                activeChatIdStorage.value = "0";
            }
        } catch (error) {
            console.error("加载聊天列表失败:", error);
        } finally {
            isLoadingChats.value = false;
        }
    }

    async function activateChat(id: string, isNewChat: boolean = false) {
        if (activeChatIdStorage.value === id && !isNewChat) return;

        activeChatIdStorage.value = id;
        const chat = chatList.value.find((c) => c.id === id);
        if (!chat) return;

        if (isNewChat) {
            chat.msgList = [];
            isLoadingMessages.value = false;
        } else {
            isLoadingMessages.value = true;
            try {
                // 使用新的适配器加载聊天消息
                const result = await chatStoreAdapter.loadChat(id);
                chat.msgList = result.messages;
            } catch (error) {
                console.error(`加载 [${id}] 的消息失败:`, error);
                chat.msgList = []; // Clear on error
            } finally {
                isLoadingMessages.value = false;
            }
        }
    }

    async function createChat() {
        // 使用新的适配器创建聊天
        const newChat = await chatStoreAdapter.createChat();
        chatList.value.push(newChat);
        return newChat;
    }

    async function removeChat(id: string) {
        const idx = chatList.value.findIndex((chat) => chat.id === id);
        if (idx === -1) return;

        try {
            // 使用新的适配器删除聊天
            await chatStoreAdapter.removeChat(id);

            // 从本地列表中移除
            chatList.value.splice(idx, 1);

            // 如果删除的是当前活动聊天，切换到其他聊天
            if (id === activeChatId.value) {
                const newActiveId =
                    chatList.value.length > 0 ? chatList.value[0].id : "0";
                await activateChat(newActiveId, false);
            }
        } catch (error) {
            console.error('Error removing chat:', error);
            throw error;
        }
    }

    async function updateChat(id: string, chat: Partial<Chat>) {
        const idx = chatList.value.findIndex((item) => item.id === id);
        if (idx > -1) {
            try {
                // 使用新的适配器更新聊天
                await chatStoreAdapter.updateChat(id, chat);

                // 更新本地状态
                Object.assign(chatList.value[idx], chat);
            } catch (error) {
                console.error('Error updating chat:', error);
                throw error;
            }
        }
    }

    // --- Message Management ---

    function addMessage(chatId: string, message: Msg) {
        const chat = chatList.value.find((c) => c.id === chatId);
        if (chat) {
            chat.msgList.push(message);
            chat.messageCount = (chat.messageCount || 0) + 1;
            chat.lastMsgPreview = message.short;
        }
    }

    function updateMessage(chatId: string, message: Msg) {
        console.log(`[ChatStore] updateMessage for chatId: ${chatId}, status: ${message.status}`, JSON.stringify(message));
        const chat = chatList.value.find((c) => c.id === chatId);
        if (chat) {
            const msgIndex = chat.msgList.findIndex((m) => m.id === message.id);
            if (msgIndex > -1) {
                const oldStatus = chat.msgList[msgIndex].status;
                // 使用 Vue 的响应式方式更新数组元素
                chat.msgList.splice(msgIndex, 1, message);
                console.log(`[ChatStore] Message updated - Status changed from ${oldStatus} to ${message.status}`);
                // If it's the last message, update preview
                if (msgIndex === chat.msgList.length - 1) {
                    chat.lastMsgPreview = message.short;
                }
            } else {
                console.warn(`[ChatStore] Message with id ${message.id} not found in chat ${chatId}`);
            }
        } else {
            console.warn(`[ChatStore] Chat with id ${chatId} not found`);
        }
    }

    async function handleSendMessage(content: MessageContent, resend: boolean = false, assistantMessageId?: string) {
        isLoading.value = true;

        try {
            if (!resend && !content) {
                throw new Error("消息不能为空");
            }

            // 检查模型配置
            const openai = await modelStore.createOpenAIInstance();
            if (!openai) {
                const errorReason = "无法创建 OpenAI 实例，请检查供应商配置。";
                message.error(errorReason);
                throw new Error(errorReason);
            }

            // 确保有活动的聊天
            let currentChatId = activeChatId.value;
            if (currentChatId === "0") {
                const newChat = await chatStoreAdapter.createChat();
                chatList.value.push(newChat);
                await activateChat(newChat.id, true);
                currentChatId = newChat.id;
            }

            let fullResponse = "";

            if (resend && assistantMessageId) {
                // 重新发送逻辑：删除消息并重新发送
                await chatStoreAdapter.resendMessage(
                    currentChatId,
                    assistantMessageId,
                    {
                        onUserMessageCreated: (userMessage) => {
                            console.log("[ChatStore] User message created (resend):", userMessage.id);
                            addMessage(currentChatId, userMessage);
                        },
                        onAssistantMessageCreated: (assistantMessage) => {
                            console.log("[ChatStore] Assistant message created (resend):", {
                                id: assistantMessage.id,
                                status: assistantMessage.status,
                                role: assistantMessage.role
                            });
                            addMessage(currentChatId, assistantMessage);
                        },
                        onMessageUpdate: (updatedMessage) => {
                            console.log("[ChatStore] Message updated (resend):", {
                                id: updatedMessage.id,
                                status: updatedMessage.status,
                                role: updatedMessage.role,
                                hasContent: !!updatedMessage.content,
                                hasThinkContent: !!updatedMessage.thinkContent
                            });
                            updateMessage(currentChatId, updatedMessage);

                            // 累积完整响应用于后续的标题生成
                            if (updatedMessage.content) {
                                const processed = processContent(updatedMessage.content);
                                fullResponse = processed.mainContent + (processed.thinkContent || '');
                            }
                        },
                        onMessagesDeleted: (deletedMessageIds) => {
                            console.log("[ChatStore] Messages deleted (resend):", deletedMessageIds);
                            // 从 UI 中移除已删除的消息
                            const chat = chatList.value.find((c) => c.id === currentChatId);
                            if (chat) {
                                chat.msgList = chat.msgList.filter(msg => !deletedMessageIds.includes(msg.id));
                                chat.messageCount = Math.max(0, (chat.messageCount || 0) - deletedMessageIds.length);
                            }
                        },
                    }
                );
            } else {
                // 正常发送消息逻辑
                await chatStoreAdapter.sendMessage(
                    currentChatId,
                    content,
                    {
                        resend: false,
                        onUserMessageCreated: (userMessage) => {
                            console.log("[ChatStore] User message created:", userMessage.id);
                            addMessage(currentChatId, userMessage);
                        },
                        onAssistantMessageCreated: (assistantMessage) => {
                            console.log("[ChatStore] Assistant message created:", {
                                id: assistantMessage.id,
                                status: assistantMessage.status,
                                role: assistantMessage.role
                            });
                            addMessage(currentChatId, assistantMessage);
                        },
                        onMessageUpdate: (updatedMessage) => {
                            console.log("[ChatStore] Message updated:", {
                                id: updatedMessage.id,
                                status: updatedMessage.status,
                                role: updatedMessage.role,
                                hasContent: !!updatedMessage.content,
                                hasThinkContent: !!updatedMessage.thinkContent
                            });
                            updateMessage(currentChatId, updatedMessage);

                            // 累积完整响应用于后续的标题生成
                            if (updatedMessage.content) {
                                const processed = processContent(updatedMessage.content);
                                fullResponse = processed.mainContent + (processed.thinkContent || '');
                            }
                        },
                    }
                );
            }

            // 消息发送完成，处理标题生成
            const isFirstTurn = (activeChat.value?.messageCount ?? 0) <= 2;
            if (isFirstTurn && fullResponse) {
                // 使用主要内容生成标题，过滤掉思考内容
                const processed = processContent(fullResponse);
                const mainContentForTitle = processed.mainContent || fullResponse;

                let summary = "";
                if (enableAiSummary.value) {
                    try {
                        summary = await summaryService.generateSummary(
                            openai,
                            modelStore.model,
                            mainContentForTitle
                        );
                    } catch (e) {
                        console.error("生成摘要失败", e);
                        summary = mainContentForTitle.substring(0, 20);
                    }
                } else {
                    summary = mainContentForTitle.substring(0, 20);
                }

                if (summary) {
                    await updateChat(currentChatId, {
                        short: summary,
                    });
                }
            }

        } catch (error) {
            const err = error instanceof Error ? error : new Error("An unknown error occurred");
            console.error("[ChatStore] handleSendMessage error:", err);
            message.error(err.message);
        } finally {
            isLoading.value = false;
        }
    }

    // --- Computed Properties & Refs ---

    const activeChatId = computed(() => activeChatIdStorage.value);

    const activeChat: ComputedRef<Chat> = computed(() => {
        return (
            chatList.value.find((item) => item.id === activeChatId.value) || {
                id: "0",
                msgList: [],
                short: "",
                time: 0,
                messageCount: 0,
                lastMsgPreview: "",
            }
        );
    });

    const scrollTo = ref(
        (position = "bottom", onlyScrollWhenAtBottom = false) => {
            console.warn("未初始化滚动函数", position, onlyScrollWhenAtBottom);
        }
    );

    // --- Initial Load ---
    loadAllChats();

    return {
        chatList,
        createChat,
        removeChat,
        updateChat,
        activateChat,
        activeChat,
        activeChatId,
        scrollTo,
        isLoading,
        isSidebarCollapsed,
        maxMsgCount,
        isLoadingMessages,
        isLoadingChats,
        loadAllChats,
        addMessage,
        updateMessage,
        enableAiSummary,
        handleSendMessage,
        // 回复风格相关
        replyStyle,
    };
});
