<script setup lang="ts">
import {
    ArrowCircleUp16Filled,
    Image24Regular,
    Dismiss24Regular,
    TextBulletListLtr24Regular,
} from "@vicons/fluent";
import { ref, computed, nextTick } from "vue";
import { useMessage, NButton, NIcon, NImage, NDropdown } from "naive-ui";
import { useChatStore, REPLY_STYLE_LABELS, type ReplyStyle } from "@/store/useChatStore.ts";
import { useEditAreaStore } from "@/store/useEditAreaStore.ts"; // 使用专用 store
import { ImageData, MessageContent } from "@/core/types/chat.ts";
import { usePendingImages } from "@/components/EditArea/usePendingImages.ts"; // 导入图片处理 hooks（原生 input 方案）
import { useInputFocus } from "@/components/EditArea/useInputFocus.ts"; // 导入聚焦hooks
import { useKeyboardHandlers } from "./useKeyboardHandlers.ts"; // 导入按键处理hooks

const message = useMessage();
const chatStore = useChatStore();
const editAreaStore = useEditAreaStore();

// 回复风格选项
const replyStyleOptions = [
    {
        label: REPLY_STYLE_LABELS.default,
        key: "default" as ReplyStyle,
    },
    {
        label: REPLY_STYLE_LABELS.concise,
        key: "concise" as ReplyStyle,
    },
    {
        label: REPLY_STYLE_LABELS.detailed,
        key: "detailed" as ReplyStyle,
    },
];

const handleReplyStyleSelect = (key: string) => {
    chatStore.replyStyle = key as ReplyStyle;
};

// Use the composable for image handling
const {
    pendingImages,
    isProcessingImage,
    isDragOver,
    openFileSelector,
    removePendingImage,
    clearPendingImages,
    handleDragOver,
    handleDragLeave,
    handleDrop,
} = usePendingImages();

// 创建computed值用于监听会话切换
const activeChatId = computed(() => chatStore.activeChatId);

// 使用聚焦hooks，监听会话切换时自动聚焦
const { inputRef, focusInput } = useInputFocus(activeChatId);

// 使用按键处理hooks
const { handleKeyDown } = useKeyboardHandlers({
    inputRef,
    getCurrentValue: () => editAreaStore.inputValue,
    setInputValue: (value: string) => {
        editAreaStore.inputValue = value;
    },
    onSend: handleSend,
});

async function handleSend() {
    const textInput = editAreaStore.inputValue.trim();
    if (!textInput && pendingImages.value.length === 0) {
        message.info("请输入内容或选择图片");
        return;
    }

    if (chatStore.isLoading) {
        message.error("正在等待 AI 响应，请稍后再试");
        return;
    }

    try {
        const contentParts: (string | ImageData)[] = [];
        if (textInput) {
            contentParts.push(textInput);
        }
        pendingImages.value.forEach((pImage) => {
            contentParts.push({
                type: "image",
                imageId: pImage.imageId,
                alt: pImage.alt,
                mimeType: pImage.mimeType,
            });
        });

        let messageToSend: MessageContent;
        if (contentParts.length === 0) {
            message.info("没有内容可发送");
            return;
        } else if (contentParts.length === 1) {
            messageToSend = contentParts[0];
        } else {
            messageToSend = contentParts;
        }

        const inputToSend = messageToSend;
        editAreaStore.clearInput();
        clearPendingImages();

        await chatStore.handleSendMessage(inputToSend);
    } catch (e: any) {
        console.error("EditArea handleSend error:", e);
        message.error(e.message || "请求失败，请稍后再试");
    } finally {
        // isLoading state is now managed within the store
    }
}
</script>

<template>
    <div class="flex flex-col px-0 py-[10px_0_20px_0] w-full mb-4">
        <!-- Pending Images Preview Area -->
        <div
            v-if="pendingImages.length > 0"
            class="pending-images-container mb-2 p-2 editarea-pending-image-container-border rounded-[10px] flex flex-wrap gap-2"
        >
            <div
                v-for="(pImage, index) in pendingImages"
                :key="pImage.imageId"
                class="pending-image-item relative"
            >
                <n-image
                    :src="pImage.previewUrl"
                    :alt="pImage.alt"
                    object-fit="cover"
                    class="w-20 h-20 rounded editarea-pending-image-border"
                />
                <n-button
                    circle
                    size="tiny"
                    type="error"
                    @click="removePendingImage(pImage, index)"
                    class="absolute top-[-5px] right-[-5px] z-10"
                    title="移除图片"
                >
                    <template #icon>
                        <n-icon :component="Dismiss24Regular" />
                    </template>
                </n-button>
            </div>
        </div>

        <div
            class="relative w-full rounded-[15px] editarea-input-container-border editarea-input-container-bg overflow-hidden transition-all shadow-sm dark:shadow-none"
            :class="{ 'drag-over': isDragOver }"
            @dragover="handleDragOver"
            @dragleave="handleDragLeave"
            @drop="handleDrop"
        >
            <n-input
                ref="inputRef"
                v-model:value="editAreaStore.inputValue"
                type="textarea"
                placeholder="想问点什么"
                size="large"
                :autosize="{
                    minRows: 1,
                    maxRows: 10,
                }"
                @keydown="handleKeyDown"
                class="w-full input-wrap"
                :bordered="false"
            />
            <div
                class="flex justify-between items-center px-4 py-2 editarea-input-toolbar-border"
            >
                <div class="flex items-center gap-2">
                    <div
                        class="flex items-center pt-1 pb-1 pl-2 pr-2 rounded-full hover:bg-[var(--icon-hover-color)] cursor-pointer"
                        @click="openFileSelector"
                    >
                        <!-- 已切换为原生 input[type='file'] 方案，避免 vueuse useFileDialog 问题 -->
                        <n-icon
                            class="text-[var(--icon-color)] hover:cursor-pointer"
                            :size="20"
                            title="上传图片"
                        >
                            <Image24Regular />
                        </n-icon>
                        <span class="text-[var(--icon-color)] pl-1">图片</span>
                    </div>

                    <!-- 回复风格选择器 -->
                    <n-dropdown
                        :options="replyStyleOptions"
                        @select="handleReplyStyleSelect"
                        trigger="hover"
                    >
                        <div
                            class="flex items-center pt-1 pb-1 pl-2 pr-2 rounded-full hover:bg-[var(--icon-hover-color)] cursor-pointer"
                            :title="`回复风格: ${REPLY_STYLE_LABELS[chatStore.replyStyle]}`"
                        >
                            <n-icon
                                class="text-[var(--icon-color)] hover:cursor-pointer"
                                :size="20"
                            >
                                <TextBulletListLtr24Regular />
                            </n-icon>
                            <span class="text-[var(--icon-color)] pl-1">{{ REPLY_STYLE_LABELS[chatStore.replyStyle] }}</span>
                        </div>
                    </n-dropdown>
                </div>
                <div class="flex items-center">
                    <n-icon
                        class="editarea-send-btn"
                        :size="36"
                        :class="{
                            disabled:
                                !editAreaStore.hasContent() &&
                                pendingImages.length === 0,
                        }"
                        @click="handleSend"
                    >
                        <ArrowCircleUp16Filled />
                    </n-icon>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped lang="less">
@import "@/assets/token.less";

/* 输入框透明背景 */
.input-wrap {
    background-color: transparent !important;
}

/* 待发送图片容器边框 */
.editarea-pending-image-container-border {
    border: 1px solid var(--pending-image-container-border);
}

/* 图片预览边框 */
.editarea-pending-image-border {
    border: 1px solid var(--pending-image-border);
}

/* 输入框容器边框 */
.editarea-input-container-border {
    border: 1px solid var(--input-container-border);
}

/* 输入框容器背景 */
.editarea-input-container-bg {
    background-color: var(--input-container-bg);
}

/* 工具栏边框 */
// .editarea-input-toolbar-border {
//     border-top: 1px solid var(--input-toolbar-border);
// }

/* 发送按钮颜色（主色/悬停/禁用） */
.editarea-send-btn {
    color: var(--button-text-primary);
    cursor: pointer;
    transition: color 0.2s;
    &:hover {
        color: var(--button-text-hover);
    }
    &.disabled {
        color: var(--button-text-disabled);
        cursor: not-allowed;
        pointer-events: none;
    }
}

/* 拖拽悬停效果 */
.drag-over {
    border-color: var(--primary-color) !important;
    background-color: var(--primary-color-hover) !important;
    box-shadow: 0 0 0 2px var(--primary-color-pressed) !important;
}
</style>
