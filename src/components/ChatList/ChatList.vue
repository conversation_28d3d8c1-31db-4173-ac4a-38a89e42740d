<script setup lang="ts">
import { onMounted, computed, ref, watch, h } from "vue";
import { useEnvStore } from "../../store/useEnvStore";
import { useChatStore } from "../../store/useChatStore.ts";
import { Chat } from "@/core/types/chat.ts";
import ChatItem from "./ChatItem.vue";
import { useRouter, useRoute } from "vue-router";
import { Pig } from "@vicons/tabler";
import { HelpCircleOutline } from "@vicons/ionicons5";
import { useGuideStore } from "@/store/useGuideStore.ts";
import { useMessage } from "naive-ui";
import RenameChatModal from "./RenameChatModal.vue";

const showBottomContent = ref(true);

const router = useRouter();
const route = useRoute();
const message = useMessage();

// 重命名模态框相关状态
const showRenameModal = ref(false);
const currentRenameChat = ref<Chat | null>(null);

function handleSettingClick() {
    router.push({ name: "Setting" });
}

// 移除 dropdownOptions

function onTransitionLeave() {
    showBottomContent.value = false;
}

function onTransitionEnter() {
    showBottomContent.value = true;
}

const store = useChatStore();
const envStore = useEnvStore();
const guideStore = useGuideStore();

// 监听路由变化，当路由不是根路径时折叠侧边栏
watch(
    () => route.path,
    (newPath) => {
        if (newPath !== "/") {
            store.isSidebarCollapsed = true;
        }
    },
    { immediate: true } // 立即执行一次，确保组件挂载时也能正确应用折叠逻辑
);

/**
 * 右键菜单
 */
// @ts-ignore
async function handleRightBtnClick(e, itemId) {
    e.preventDefault();
    // 根据ID找到对应的聊天项
    const item = store.chatList.find((chat) => chat.id == itemId);
    if (!item) return;

    try {
        // 导入必要的Tauri菜单API
        const { Menu } = await import("@tauri-apps/api/menu");

        // 创建菜单实例
        const menu = await Menu.new({
            items: [
                {
                    text: "重命名",
                    action: () => {
                        handleRenameChatItem(item);
                    },
                },
                {
                    text: "删除会话",
                    action: () => {
                        handleDeleteChatItem(item);
                    },
                },
            ],
        });

        // 显示菜单
        await menu.popup();
    } catch (error) {
        console.error("创建菜单失败:", error);
    }
}

onMounted(() => {
    // window.electron.ipcRenderer.on('menu-click-callback', (e, chatId) => {
    //     store.removeChat(chatId)
    // })

    // 检查初始窗口宽度
    if (window.innerWidth < 768) {
        store.isSidebarCollapsed = true;
    }
});

function handleDeleteChatItem(item: Chat) {
    console.log("删除会话", item);
    store.removeChat(item.id);
}

function handleRenameChatItem(item: Chat) {
    console.log("重命名会话", item);
    currentRenameChat.value = item;
    showRenameModal.value = true;
}

// 处理重命名确认
async function handleRenameConfirm(chatId: string, newTitle: string) {
    try {
        await store.updateChat(chatId, { short: newTitle });
        message.success("重命名成功");
    } catch (error) {
        console.error("重命名失败:", error);
        message.error("重命名失败");
    }
}

async function handleClickChatItem(item: Chat) {
    await store.activateChat(item.id, false);
    if (envStore.isM) {
        store.isSidebarCollapsed = true;
    }
}

const showChatList = computed(() => {
    const list = [...store.chatList];
    list.sort((a, b) => b.time - a.time);
    return list;
});
</script>

<template>
    <n-flex
        vertical
        class="side-container"
        :class="{ collapsed: store.isSidebarCollapsed }"
    >
        <n-flex
            vertical
            class="side border-gray-300 dark:border-black border-r-1"
            :class="{
                light: envStore.themeStatus === 'light',
                win: envStore.isWin,
                collapsed: store.isSidebarCollapsed,
            }"
        >
            <div class="min-h-14" data-tauri-drag-region></div>
            <div class="list">
                <ChatItem
                    v-for="item in showChatList"
                    :key="item.id"
                    :item="item"
                    :is-active="item.id === store.activeChatId"
                    @click="handleClickChatItem"
                    @contextmenu="handleRightBtnClick"
                    @delete="handleDeleteChatItem"
                />
            </div>

            <transition
                name="fade"
                @leave="onTransitionLeave"
                @after-enter="onTransitionEnter"
            >
                <div
                    v-if="!store.isSidebarCollapsed"
                    class="bottom-container"
                ></div>
            </transition>
            <div class="bottom-section" v-if="!store.isSidebarCollapsed">
                <n-flex vertical style="padding: 12px" :wrap="false">
                    <n-flex
                        align="center"
                        class="user-info"
                        @click="guideStore.openGuide"
                    >
                        <n-icon size="18" class="icon">
                            <HelpCircleOutline />
                        </n-icon>
                        <p>新手引导</p>
                    </n-flex>
                    <n-flex
                        align="center"
                        class="user-info"
                        @click="handleSettingClick"
                    >
                        <n-icon size="18" class="icon">
                            <Pig />
                        </n-icon>
                        <p>PigAI Beta</p>
                    </n-flex>
                </n-flex>
            </div>
            <!-- 跳转路由后不再渲染弹窗组件 -->
        </n-flex>

        <!-- 重命名会话模态框 -->
        <RenameChatModal
            v-model:show="showRenameModal"
            :chat-item="currentRenameChat"
            @confirm="handleRenameConfirm"
        />
    </n-flex>
</template>

<style scoped lang="less">
.side-container {
    width: 220px;
    height: 100%;
    flex-shrink: 0;
    overflow: hidden;
    transition: width 0.2s ease;

    &:has(.side.collapsed) {
        width: 0;
    }
}

.side {
    display: flex;
    width: 220px;
    height: 100%;
    flex-shrink: 0;
    gap: 0 !important;
    background-color: var(--side-bg-color);
    color: var(--side-text-color);
    transform: translateX(0);
    transition: transform 0.3s ease, opacity 0.3s ease;

    .bottom-section {
        .user-info {
            width: 100%;
            padding: 8px;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.3s ease;

            &:hover {
                background-color: var(--side-hover-bg-color);
            }

            p {
                margin-left: 8px;
            }
        }
    }

    @media (max-width: 768px) {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 100;
    }

    .fade-enter-active,
    .fade-leave-active {
        transition: opacity 0.3s ease;
    }

    .fade-enter-from,
    .fade-leave-to {
        opacity: 0;
    }

    .bottom-container {
        display: flex;
        flex-direction: column;
    }

    &.collapsed {
        transform: translateX(-100%);
        opacity: 0;
    }

    .top {
        flex-shrink: 0;
        padding: 16px;
        width: 100%;
        display: flex;
        align-items: center;
        position: relative;

        .title {
            display: flex;
            gap: 8px;
            font-size: 16px;
            font-weight: 600;

            .icon {
                box-sizing: content-box;
                cursor: pointer;
                padding: 6px;
                border-radius: 9px;
                transition: background-color 0.3s;
                color: var(--icon-color);

                @media screen and (min-width: 768px) {
                    &:hover {
                        background-color: var(--icon-hover-color);
                    }
                }
            }

            .title-exp {
                font-size: 14px;
                font-weight: bolder;
                color: #ff0000;
            }
        }
    }

    .list {
        flex-grow: 1;
        overflow-y: auto;

        &::-webkit-scrollbar {
            background-color: transparent;
            width: 8px;
        }

        &::-webkit-scrollbar-thumb {
            background-color: #8d8d93;
            border-radius: 5px;
        }
    }

    .bottom {
        margin: 20px;
        font-size: 12px;
        display: grid;
        gap: 8px;
    }

    &.light {
        color: var(--side-text-color);

        .item {
            p {
                color: var(--side-text-color);
            }
        }
    }
}
</style>
