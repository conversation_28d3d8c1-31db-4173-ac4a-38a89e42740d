<script setup lang="ts">
import { nextTick, Ref, ref, watch, onMounted, onUnmounted } from "vue";
import { useMessage } from "naive-ui";
import { useChatStore } from "@/store/useChatStore.ts";
import { Msg, MessageStatus } from "@/core/types/chat.ts";
import MsgItem from "@/components/MsgItem/MsgItem.vue";
import { useScroll } from "@vueuse/core";

const store = useChatStore();
const message = useMessage();
const msgListRef: Ref<HTMLDivElement | null> = ref(null);

// 滚动状态管理
const isUserScrolling = ref(false);
const userScrollTimer = ref<number | null>(null);
const lastScrollTop = ref(0);
const autoScrollEnabled = ref(true);

const { arrivedState, y: scrollY } = useScroll(msgListRef, {
    offset: {
        bottom: 50, // 增加底部阈值到50px
    },
});

// 检测用户主动滚动
const detectUserScroll = () => {
    if (!msgListRef.value) return;

    const currentScrollTop = msgListRef.value.scrollTop;
    const scrollDiff = Math.abs(currentScrollTop - lastScrollTop.value);

    // 如果滚动距离大于阈值，认为是用户主动滚动
    if (scrollDiff > 10) {
        isUserScrolling.value = true;
        autoScrollEnabled.value = false;

        // 清除之前的定时器
        if (userScrollTimer.value) {
            clearTimeout(userScrollTimer.value);
        }

        // 1秒后重置用户滚动状态
        userScrollTimer.value = window.setTimeout(() => {
            isUserScrolling.value = false;
            // 如果用户滚动到底部附近，重新启用自动滚动
            if (arrivedState.bottom) {
                autoScrollEnabled.value = true;
            }
        }, 1000);
    }

    lastScrollTop.value = currentScrollTop;
};

// 智能滚动函数
const smartScrollToBottom = (force: boolean = false) => {
    if (!msgListRef.value) return;

    // 强制滚动或者满足自动滚动条件时才滚动
    if (force || (autoScrollEnabled.value && !isUserScrolling.value)) {
        msgListRef.value.scrollTo({
            top: msgListRef.value.scrollHeight,
            behavior: 'smooth'
        });
    }
};

store.scrollTo = (
    _position = "bottom",
    onlyScrollWhenAtBottom: boolean = false
) => {
    if (onlyScrollWhenAtBottom && !arrivedState.bottom) {
        return;
    }
    smartScrollToBottom(true);
};

async function handleRetry(item: Msg) {
    if (store.isLoading) {
        message.error("正在等待 AI 响应，请稍后再试");
        return;
    }

    try {
        await store.handleSendMessage("", true, item.id);
    } catch (e: any) {
        console.error("MsgList handleRetry error:", e);
        message.error(e.message || "网络错误，请稍后再试");
    }
}

// 监听聊天切换
watch(
    () => store.activeChatId,
    async () => {
        await nextTick();
        autoScrollEnabled.value = true;
        isUserScrolling.value = false;
        smartScrollToBottom(true);
    },
    { immediate: true }
);

// 监听消息列表变化，实现智能滚动
watch(
    () => store.activeChat.msgList,
    async (newMsgList, oldMsgList) => {
        await nextTick();

        // 如果有新消息且AI正在生成，启用自动滚动
        if (newMsgList.length > (oldMsgList?.length || 0)) {
            const lastMsg = newMsgList[newMsgList.length - 1];
            if (lastMsg?.role === 'assistant' &&
                (lastMsg?.status === MessageStatus.THINKING || lastMsg?.status === MessageStatus.GENERATING)) {
                autoScrollEnabled.value = true;
            }
        }

        smartScrollToBottom();
    },
    { deep: true }
);

// 监听滚动位置变化
watch(scrollY, () => {
    detectUserScroll();

    // 如果用户滚动到底部，重新启用自动滚动
    if (arrivedState.bottom && !isUserScrolling.value) {
        autoScrollEnabled.value = true;
    }
});

onMounted(() => {
    // 初始化时滚动到底部
    nextTick(() => {
        smartScrollToBottom(true);
    });
});

onUnmounted(() => {
    if (userScrollTimer.value) {
        clearTimeout(userScrollTimer.value);
    }
});
</script>

<template>
    <div ref="msgListRef" class="msg-list" style="flex-grow: 1">
        <msg-item
            v-for="item in store.activeChat.msgList"
            :key="item.id"
            :item="item"
            @retry="handleRetry"
        />
    </div>
</template>

<style scoped lang="less">
@import "../../assets/function.less";

// 在样式中使用 mixin
.msg-list {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 20px;
    user-select: text;
    font-size: 16px;

    .scrollbar-style();
}
</style>
